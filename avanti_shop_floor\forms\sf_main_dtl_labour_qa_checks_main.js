/**
 * @properties={typeid:35,uuid:"BB353018-F549-4131-9288-22EE7D2E1684",variableType:-4}
 */
var bHitOK = false;

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"503D8FBB-DDD8-4B9B-B746-673F328A91E9"}
 */
function onActionOK(event) {
	for(var fs_idx = 1; fs_idx <= foundset.getSize(); fs_idx++) {
		if(foundset.getRecord(fs_idx).qa_check_pos_1 && foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1_placehold == 0){
			globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
				i18n.getI18NMessage('avanti.dialog.shopFloorConfirmQAChecks_Resource'),
				i18n.getI18NMessage('avanti.dialog.ok'))
			return false
		}
		
		if(foundset.getRecord(fs_idx).qa_check_pos_2 && foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2_placehold == 0){
			globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
				i18n.getI18NMessage('avanti.dialog.shopFloorConfirmQAChecks_Resource'),
				i18n.getI18NMessage('avanti.dialog.ok'))
			return false
		}
		
		if(foundset.getRecord(fs_idx).qa_check_pos_3 && foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3_placehold == 0){
			globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
				i18n.getI18NMessage('avanti.dialog.shopFloorConfirmQAChecks_Resource'),
				i18n.getI18NMessage('avanti.dialog.ok'))
			return false
		}
	}
	resetPlaceHolder()
	bHitOK=true
	return globals.DIALOGS.closeForm(null, 'QA Checks'); // GD - Apr 4, 2014: Just added a return to clear the jsDoc warning
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"0A447EC6-DB20-4BD8-B3FC-919552B28C22"}
 */
function onActionCancel(event) {
	resetPlaceHolder()
	bHitOK=false
	globals.DIALOGS.closeForm(event);
	return false
}

/**
 * @properties={typeid:24,uuid:"0B606877-FD31-4FCD-8BD5-1DC6FEC1C95C"}
 */
function resetPlaceHolder() {
	for(var fs_idx = 1; fs_idx <= foundset.getSize(); fs_idx++) {
		foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1_placehold = 0
		foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2_placehold = 0
		foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3_placehold = 0
		
	}
}

/**
 * @param event
 *
 * @properties={typeid:24,uuid:"8CF8BF3B-B583-4A3E-A87C-61B5449E001A"}
 */
function onRenderCheckOld(event) {
//	var check_field = event.getRenderable()
//	var sElementName = check_field.getName()
//	
//	if(_sRenderedItems.indexOf(sElementName) > -1){
//		return // only need to process onRenderCheck once - setting check_field.visible = true below causes onRenderCheck() to fire again
//	}
//	else{
//		_sRenderedItems += sElementName
//
//		if(sElementName.indexOf('qa_check_field') == -1){ // check box, not label, get name and it will be asigned when label turns comes around
//			/***@type {JSRecord<db:/avanti/sch_milestone_group>} */
//			var currentRowRecord = event.getRecord()
//			_aCheckNames.push(getNextQACheck(currentRowRecord))
//		}
//		
//		var iCheckNum = sElementName.substr(sElementName.length-1, 1) 
//		
//		if(_aCheckNames[iCheckNum]){
//			if(sElementName.indexOf('qa_check_field') > -1){ // label - get the name
//				check_field.putClientProperty('text', _aCheckNames[iCheckNum])
//			}
//			check_field.visible = true		
//		}
//		else{
//			check_field.visible = false	
//		}
//	}
}

/**
 * @return {String}
 *
 * @param {JSRecord<db:/avanti/sch_milestone_group>} currentRowRecord
 *
 * @properties={typeid:24,uuid:"C8DB79D8-4748-4816-B84A-F4342C169CC0"}
 */
function getNextQACheck(currentRowRecord){
	_iLastCheckNumAdded = 0
	if(currentRowRecord && utils.hasRecords(currentRowRecord.sch_milestone_group_to_sys_cost_centre)) {
		if(_iLastCheckNumAdded  < 1 && currentRowRecord.sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1_enabled) {
			_iLastCheckNumAdded = 1
			return currentRowRecord.sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1
		} 
		else if(_iLastCheckNumAdded  < 2 && currentRowRecord.sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2_enabled) {
			_iLastCheckNumAdded = 2
			return currentRowRecord.sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2
		} 
		else if(_iLastCheckNumAdded  < 3 && currentRowRecord.sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3_enabled) {
			_iLastCheckNumAdded = 3
			return currentRowRecord.sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3
		}		
	} 
	
	return ''
}

/**
 * Called before the form component is rendered.
 *
 * @param {JSRenderEvent} event the render event
 *
 * @properties={typeid:24,uuid:"6C264BE6-8780-48C3-A5A2-4056D77576B6"}
 */
function onRenderCC(event) {
	_iLastCheckNumAdded = 0
	_sRenderedItems = ''
	_aCheckNames = []
}

/**
 * Called before the form component is rendered.
 *
 * @param {JSRenderEvent} event the render event
 *
 * @properties={typeid:24,uuid:"F9F245F4-9057-4537-9CEA-26BE9A4D1AEA"}
 */
function onRenderCheck1(event) {
	/***@type {JSRecord<db:/avanti/sch_milestone_group>} */
	var currentRowRecord = event.getRecord()
	var check_field = event.getRenderable()
	check_field.visible = true
	
	if(currentRowRecord && !currentRowRecord.qa_check_pos_1){
		check_field.visible = false
	}
}

/**
 * Called before the form component is rendered.
 *
 * @param {JSRenderEvent} event the render event
 *
 * @properties={typeid:24,uuid:"2F2330BA-60CB-4758-AC81-DF5BDCE518F6"}
 */
function onRenderCheck2(event) {
	/***@type {JSRecord<db:/avanti/sch_milestone_group>} */
	var currentRowRecord = event.getRecord()
	var check_field = event.getRenderable()
	check_field.visible = true

	if(currentRowRecord && !currentRowRecord.qa_check_pos_2){
		check_field.visible = false
	}
}

/**
 * Called before the form component is rendered.
 *
 * @param {JSRenderEvent} event the render event
 *
 * @properties={typeid:24,uuid:"EC4C9182-BC45-4CB2-9826-98CCD3BE2E7D"}
 */
function onRenderCheck3(event) {
	/***@type {JSRecord<db:/avanti/sch_milestone_group>} */
	var currentRowRecord = event.getRecord()
	var check_field = event.getRenderable()
	
	check_field.visible = true
	if(currentRowRecord && !currentRowRecord.qa_check_pos_3){
		check_field.visible = false
	}
	
	

//	check_field.visible = false
//	if(currentRowRecord && currentRowRecord.qa_check_pos_3){
//		application.output('currentRowRecord.qa_check_pos_3: ' + currentRowRecord.qa_check_pos_3)
//		check_field.visible = true
//	}
	
}

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"4F16DAFC-16DF-4404-B3A8-C7C1C901DC32"}
 */
function onShow(firstShow, event) {
	if(!qa_check_pos_1){
		elements.qa_check_1.visible = false
	}

	if(!qa_check_pos_2){
		elements.qa_check_2.visible = false
	}

	if(!qa_check_pos_3){
		elements.qa_check_2.visible = false
	}
}
