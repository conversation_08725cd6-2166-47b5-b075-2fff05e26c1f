customProperties:"methods:{\
onShowMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
dataSource:"db:/avanti/sa_invoice",
extendsID:"EDC634DF-14F0-498D-9C77-B74E9E9A5542",
initialSort:"inv_date desc",
items:[
{
height:198,
partType:8,
typeid:19,
uuid:"329FE3FC-629B-4BD8-A9BF-D9BEC8A222AB"
},
{
anchors:15,
cssPosition:"0px,0px,5px,0px,1000px,193px",
json:{
anchors:15,
columns:[
{
autoResize:true,
dataprovider:"inv_number",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.invoiceNumber",
id:"inv_number",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"5C6F3284-F9AE-4B55-8F0F-244813A97081",
valuelist:null,
visible:true,
width:140
},
{
autoResize:true,
dataprovider:"inv_date",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.invoiceDate",
id:"inv_date",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"959D4320-E0BF-49AE-ABC2-BC05A5EC16A3",
valuelist:null,
visible:true,
width:100
},
{
autoResize:true,
dataprovider:"sa_invoice_to_sa_customer_address.custaddr_code",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.billTo",
id:"custaddr_code",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"9AF31A31-1713-47CA-BAE0-D48BB1A6BA48",
valuelist:null,
visible:true,
width:140
},
{
autoResize:true,
dataprovider:"inv_address_name",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.custaddr_address_name",
id:"inv_address_name",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"739CBCFC-13E4-4FF3-9936-1435F6F1C703",
valuelist:null,
visible:true,
width:250
},
{
autoResize:false,
dataprovider:"inv_due_date",
editType:null,
enableRowGroup:true,
enableSort:true,
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.dueDate",
id:"inv_due_date",
maxWidth:140,
minWidth:140,
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"95BF3211-FF4B-4735-9DA5-B1572C76BA7E",
valuelist:null,
visible:true,
width:140
},
{
autoResize:true,
dataprovider:"inv_total_amt",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.invoiceTotal",
id:"inv_total_amt",
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"BF18590D-14C3-468C-B9CA-8A850A99F1E4",
valuelist:null,
visible:true,
width:140
},
{
autoResize:false,
dataprovider:"inv_balance_owing",
editType:null,
enableRowGroup:true,
enableSort:true,
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.totalBalance",
id:"inv_balance_owing",
maxWidth:140,
minWidth:140,
rowGroupIndex:-1,
styleClass:"text-right",
svyUUID:"719F98D8-6D8E-4AA3-85E6-D6E75B3D2160",
valuelist:null,
visible:true,
width:140
}
],
cssPosition:{
bottom:"5px",
height:"193px",
left:"0px",
right:"0px",
top:"0px",
width:"1000px"
},
enableColumnMove:true,
enableColumnResize:true,
enableSorting:true,
myFoundset:{
foundsetSelector:""
},
onReady:"F8F367BC-7E8F-44AC-B9DC-9EF400DC5AA4",
responsiveHeight:300,
rowHeight:22,
styleClass:"ag-theme-alpine",
toolPanelConfig:null,
visible:true
},
name:"grid",
styleClass:"ag-theme-alpine",
typeName:"aggrid-groupingtable",
typeid:47,
uuid:"BD2FE36A-9569-4679-9EF4-34B4F7CD8197"
},
{
height:193,
partType:5,
typeid:19,
uuid:"C2712019-8D14-42FC-82ED-7AF58D92374B"
}
],
name:"crm_customer_financial_invoice_tbl",
navigatorID:"-1",
onShowMethodID:"DF5CFC95-C2C9-42CE-9369-B02EDADC12C8",
scrollbars:33,
size:"1000,304",
styleName:null,
typeid:3,
uuid:"CE645907-3BC3-4BE6-8F75-9070DBD0D946",
view:0