/**
 * @properties={typeid:35,uuid:"CF21DDA1-7E39-4232-A839-8CFF7B054279",variableType:-4}
 */
var aArrayOfAccountingLinks = ['dept_glacctseg_id_sales','dept_glacctseg_id_wip','dept_glacctseg_id_fg','dept_glacctseg_id_cogs','dept_glacctseg_id_base','dept_glacctseg_id_labour','dept_glacctseg_id_overhead'];

/**
 *
 * @param {JSEvent} _event
 * @param {String} _triggerForm
 *
 * @return
 * @properties={typeid:24,uuid:"474F1DB9-9739-4EA8-98FB-26BE30FEC02A"}
 */
function dc_edit(_event, _triggerForm) {
    var result = _super.dc_edit(_event, _triggerForm);
    scopes.avSecurity.updateEditableFields('sys_department_dtl_accounting_links', 'bgcolor', aArrayOfAccountingLinks);
    return result;
}

/**
*
* @param {JSEvent} _event
* @param {String} _triggerForm
*
 * @return
* @properties={typeid:24,uuid:"BB1135A2-9793-4ADF-886D-4D78E30E498F"}
*/
function dc_new(_event, _triggerForm) {
   var result = _super.dc_new(_event, _triggerForm);
   scopes.avSecurity.updateEditableFields('sys_department_dtl_accounting_links', 'bgcolor', aArrayOfAccountingLinks);
   return result;
}

/**
 * @param {Boolean} _firstShow
 * @param {JSEvent} _event
 * @override
 *
 * @properties={typeid:24,uuid:"7A95358D-2354-4E73-9E97-D1543948C165"}
 */
function onShowForm(_firstShow, _event) {
	return _super.onShowForm(_firstShow, _event)
}
