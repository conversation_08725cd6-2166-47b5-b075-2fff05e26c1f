/**
 * @properties={typeid:35,uuid:"E59DC8B2-ADFB-461C-89C5-ACDC1A5CE21A",variableType:-4}
 */
var aArrayOfAccountingLinks = ['opcat_gl_sales','opcat_glacctseg_id_wip','opcat_glacctseg_id_fg','opcat_gl_cost_of_sales','opcat_glacctseg_id_base','opcat_glacctseg_id_labour','opcat_glacctseg_id_overhead'];

/**
 *
 * @param {JSEvent} _event
 * @param {String} _triggerForm
 *
 * @return
 * @properties={typeid:24,uuid:"70B21855-7652-4F8A-B91F-02A6FC096959"}
 */
function dc_new(_event, _triggerForm) {
   var result = _super.dc_new(_event, _triggerForm);
   scopes.avSecurity.updateEditableFields('sys_operation_category_dtl_accounting_links', 'bgcolor', aArrayOfAccountingLinks);
   return result;
}

/**
 *
 * @param {JSEvent} _event
 * @param {String} _triggerForm
 *
 * @return
 * @properties={typeid:24,uuid:"1F976653-80E0-476B-BF0C-C1C6F610DC76"}
 */
function dc_edit(_event, _triggerForm) {
    var result = _super.dc_edit(_event, _triggerForm);
    scopes.avSecurity.updateEditableFields('sys_operation_category_dtl_accounting_links', 'bgcolor', aArrayOfAccountingLinks);
    return result;
}

/**
 * @param {Boolean} _firstShow
 * @param {JSEvent} _event
 * @override
 *
 * @properties={typeid:24,uuid:"2880D46B-1814-4A59-AB40-B9D79DDAF25C"}
 */
function onShowForm(_firstShow, _event) {
	return _super.onShowForm(_firstShow, _event)
}
