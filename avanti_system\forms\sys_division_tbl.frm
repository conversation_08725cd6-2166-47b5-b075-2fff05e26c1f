customProperties:"methods:{\
onShowMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
dataSource:"db:/avanti/sys_division",
extendsID:"C5C6C66A-73FA-4414-B94E-4EE7D1300B84",
items:[
{
anchors:15,
cssPosition:"70px,0px,5px,0px,1000px,134px",
json:{
anchors:15,
columns:[
{
autoResize:false,
dataprovider:null,
editType:null,
enableResize:false,
enableRowGroup:false,
enableSort:false,
enableToolPanel:false,
filterType:null,
format:null,
headerStyleClass:"text-right",
headerTitle:" ",
id:"btn_template0",
maxWidth:20,
minWidth:20,
rowGroupIndex:-1,
styleClass:"material-symbols-outlined arrow_right",
svyUUID:"2E24AB7B-F6D9-4027-B4CB-4E2E53FA2F84",
valuelist:null,
visible:true,
width:20
},
{
autoResize:true,
dataprovider:"div_code",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.div_code",
id:"div_code",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"FDE4F54E-CBB6-40F3-9210-9FE6BCB67D2A",
valuelist:null,
visible:true,
width:230
},
{
autoResize:true,
dataprovider:"div_name",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.name",
id:"div_name",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"8FCE40FC-4145-4361-AB86-F46A6C3C696D",
valuelist:null,
visible:true,
width:230
},
{
autoResize:false,
dataprovider:null,
editType:null,
enableResize:false,
enableRowGroup:false,
enableSort:false,
enableToolPanel:false,
filterType:null,
format:null,
headerStyleClass:"text-left",
headerTitle:" ",
id:"sort_icon",
maxWidth:25,
minWidth:25,
rowGroupIndex:-1,
styleClass:"material-symbols-outlined swap_vert",
svyUUID:"D3E27FB7-520C-4616-AE79-7E0FBE3FAE9A",
valuelist:null,
visible:true,
width:25
}
],
cssPosition:{
bottom:"5px",
height:"134px",
left:"0px",
right:"0px",
top:"70px",
width:"1000px"
},
enableColumnMove:true,
enableColumnResize:true,
enableSorting:true,
myFoundset:{
foundsetSelector:""
},
onCellClick:"9CFC5CDD-8284-4A0E-B551-6C8B216D6282",
onCellDoubleClick:"1F7AD744-8130-4503-B636-0435A1E21484",
onReady:"C80BC8ED-5F2F-44F6-9599-3B72580E8214",
responsiveHeight:300,
rowHeight:22,
styleClass:"ag-theme-alpine",
toolPanelConfig:{
suppressColumnExpandAll:false,
suppressColumnFilter:true,
suppressColumnSelectAll:false,
suppressRowGroups:false,
suppressSideButtons:false,
svyUUID:"6538D8B6-431F-4639-9250-51DB198E3E0A"
},
visible:true
},
name:"grid",
styleClass:"ag-theme-alpine",
typeName:"aggrid-groupingtable",
typeid:47,
uuid:"6C338390-B880-4D3F-AA5C-2B5EA91C83D9"
},
{
cssPosition:"31,-1,-1,0,950,35",
json:{
containedForm:"70FCE93B-8D70-4966-B9C1-F28CBF68D584",
cssPosition:{
bottom:"-1",
height:"35",
left:"0",
right:"-1",
top:"31",
width:"950"
},
visible:true
},
name:"tabs_230",
typeName:"servoycore-formcontainer",
typeid:47,
uuid:"73BC65F0-392E-44F4-9393-CACEC81B9B0C"
},
{
height:70,
partType:1,
typeid:19,
uuid:"74EC27D1-0051-483A-B44C-66DCB9B17FA6"
},
{
cssPosition:"5,0,-1,0,1000,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"0",
right:"0",
top:"5",
width:"1000"
},
enabled:true,
styleClass:"group_heading label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.divisionTableView",
visible:true
},
name:"lblHeader",
styleClass:"group_heading label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"924BAF78-2603-4967-BD40-C17C5D46A84E"
},
{
height:209,
partType:8,
typeid:19,
uuid:"94A0C72B-83A9-4845-844B-2702314567B6"
},
{
height:204,
partType:5,
typeid:19,
uuid:"B580F3E3-362C-4802-9EE1-D243ECA7C077"
}
],
name:"sys_division_tbl",
navigatorID:"-1",
onShowMethodID:"6C82FAA9-745E-4CA6-A26C-00DADF18B3FE",
paperPrintScale:100,
scrollbars:33,
size:"1000,204",
styleName:null,
typeid:3,
uuid:"7A320277-8B0A-4897-A4F6-FB02FE900D8D",
view:0