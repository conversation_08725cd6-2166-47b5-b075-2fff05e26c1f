/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"35E15DBA-B3F0-44DD-BAF8-3CC73929AA6D",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"5239DEDA-0DD7-4926-BB15-9183F99C6D13"}
 */
function onReady() {
    _gridReady = 1;
};

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"A2778AB3-9745-4B3A-B4F2-9F6D33132BAC"}
 */
function onShow(firstShow, event) {
	if (firstShow) {
		if (!_gridReady) {
			application.executeLater(onShow, 500, [true, event]);
			return null;
		}
	}

	globals.avUtilities_shuffleGrey(controller.getName());
	return _super.onShowForm(firstShow, event);
}

/**
 * Called when the mouse is clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"BB7CD3B6-BBD0-4650-A909-21F678A5B842"}
 */
function onCellClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "btn_template0") {
		scopes.globals.svy_nav_toggleView(event);
	}
}

/**
 * Called when the mouse is double clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @SuppressWarnings(unused)
 * @properties={typeid:24,uuid:"6FD03C57-9F95-4B9F-9976-84015A6E3713"}
 */
function onCellDoubleClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell right click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	scopes.globals.svy_nav_toggleView(event)
}
