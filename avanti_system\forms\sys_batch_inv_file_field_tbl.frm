customProperties:"methods:{\
onShowMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
dataSource:"db:/avanti/sys_batch_inv_file_field",
extendsID:"BD3226CB-C29A-4CAE-A46B-646C3FE287B0",
initialSort:"sequence_nr asc",
items:[
{
cssPosition:"8,-1,-1,39,25,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"39",
right:"-1",
top:"8",
width:"25"
},
enabled:true,
onActionMethodID:"188C41BC-4282-480F-99F7-55D9B47DA93C",
styleClass:"listview_noborder label_bts",
tabSeq:0,
text:"%%globals.icon_trashCan%%",
visible:true
},
name:"btnDeleteRow",
styleClass:"listview_noborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"27E26AF8-FEC2-4C65-A39A-3087FF32DA53"
},
{
height:255,
partType:8,
typeid:19,
uuid:"42E67B63-E316-4F3B-9794-222AB395B5C4"
},
{
anchors:15,
cssPosition:"41px,0px,5px,0px,1001px,209px",
json:{
anchors:15,
columns:[
{
autoResize:true,
dataprovider:"sequence_nr",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.columnOrder",
id:"fldColOrder",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"749049BD-229A-4671-B65D-19BE96817EF3",
valuelist:null,
visible:true,
width:107
},
{
autoResize:true,
dataprovider:"biff_position",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:"#,###|#,###",
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.position",
id:"fldPosition",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"D83C0468-4F70-47F0-8934-EDAFA966ED4A",
valuelist:null,
visible:true,
width:107
},
{
autoResize:true,
dataprovider:"biff_length",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:"#,###|#,###",
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.length2",
id:"fldLength",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"15FA9F37-D5CE-4A6B-ADF8-52FCC1CA54C0",
valuelist:null,
visible:true,
width:107
},
{
autoResize:true,
dataprovider:"abif_id",
editType:"TYPEAHEAD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.avantiFieldName",
id:"fldFieldName",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"DF64DCAE-097E-4B76-BAFF-465A18E704E3",
valuelist:"97F5D6D7-CBED-4909-AB05-9242F1123D9D",
visible:true,
width:236
},
{
autoResize:true,
dataprovider:"biff_col_header",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.columnHeader",
id:"fldColHeader",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"9F7F7CC1-EEA1-4BA2-BB9C-E11D4E0A95B8",
valuelist:null,
visible:true,
width:251
},
{
autoResize:false,
dataprovider:"biff_use_udv",
editType:"CHECKBOX",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.useUDV",
id:"fldUseUDV",
maxWidth:154,
minWidth:154,
rowGroupIndex:-1,
styleClass:"text-center",
svyUUID:"AE550469-6DCF-411A-82B5-5AD375112365",
valuelist:null,
visible:true,
width:154
},
{
autoResize:true,
dataprovider:"biff_user_defined_value",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.UserDefinedValue",
id:"fldUDV",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"746EF7EB-AC57-49F6-A8A6-DB093AD101A7",
valuelist:null,
visible:true,
width:236
},
{
autoResize:true,
dataprovider:"biff_sort_num",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.sortNum",
id:"fldSortNum",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"D574E673-D990-4DE7-AFE4-54C041C94121",
valuelist:null,
visible:true,
width:108
},
{
autoResize:true,
dataprovider:"biff_sort_dir",
editType:"COMBOBOX",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.sortDirection",
id:"fldSortDir",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"2DE2E798-521C-4F25-B86F-3CFA00E5A0F8",
valuelist:"B8D64AAD-CBB6-4F2C-B787-A703F0C9F9A8",
visible:true,
width:111
},
{
autoResize:true,
dataprovider:"biff_bi_data_table_field_name",
editType:"TYPEAHEAD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.BatchInvoiceTableField",
id:"fldCBTableColName",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"E905015C-8469-47C6-A32E-E0D17C890088",
valuelist:"EEFE8191-D654-4704-8329-40ADB84F4E8F",
visible:true,
width:172
}
],
cssPosition:{
bottom:"5px",
height:"209px",
left:"0px",
right:"0px",
top:"41px",
width:"1001px"
},
enableColumnMove:true,
enableColumnResize:true,
enableSorting:true,
myFoundset:{
foundsetSelector:""
},
onCellClick:"CB82A65C-75E3-4B2A-B868-28C20951C75A",
onColumnDataChange:"FD1F4ABC-DE89-4994-B9C0-54B04AEF0E47",
onReady:"EE50F10B-BEC8-4BD2-B379-861CA7C8F3DE",
responsiveHeight:300,
rowHeight:22,
styleClass:"ag-theme-alpine",
toolPanelConfig:{
suppressColumnExpandAll:true,
suppressColumnFilter:true,
suppressColumnSelectAll:true,
suppressRowGroups:true,
suppressSideButtons:true,
svyUUID:"D26E2F85-A694-44AF-8A04-A651B40D132D"
},
visible:true
},
name:"grid",
styleClass:"ag-theme-alpine",
typeName:"aggrid-groupingtable",
typeid:47,
uuid:"4B18AE3E-77C6-41C8-94EC-A5E283A65A71"
},
{
height:41,
partType:1,
typeid:19,
uuid:"736AEA18-C35A-48F8-8277-7C1F3B5DEA49"
},
{
cssPosition:"8,-1,-1,296,118,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"296",
right:"-1",
top:"8",
width:"118"
},
enabled:true,
onActionMethodID:"913848E2-6BA1-4444-97FD-0AB2C2EDF13E",
styleClass:"btn btn-default button_bts",
tabSeq:5,
text:"i18n:avanti.lbl.ClearPositions",
visible:true
},
name:"btnClearPositions",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"82E8C25D-5B65-40E6-9C3B-0311FDA0AE00"
},
{
cssPosition:"8,-1,-1,74,100,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"74",
right:"-1",
top:"8",
width:"100"
},
enabled:true,
onActionMethodID:"4F319800-B370-453F-8337-9D298D1DCF4A",
styleClass:"btn btn-default button_bts",
tabSeq:3,
text:"i18n:avanti.lbl.moveUp",
visible:true
},
name:"component_0AD07D7A",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"B05F9F82-E862-4897-A733-DD03B22A6EB4"
},
{
cssPosition:"8,-1,-1,9,25,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"9",
right:"-1",
top:"8",
width:"25"
},
enabled:true,
onActionMethodID:"733D7F3E-2F50-445C-AD6F-4968C770C389",
styleClass:"listview_noborder label_bts",
tabSeq:1,
text:"%%globals.icon_add%%",
visible:true
},
name:"btnAdd",
styleClass:"listview_noborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"D36F7AD6-F174-414B-B01E-E4EC3E4DD718"
},
{
cssPosition:"8,-1,-1,184,100,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"184",
right:"-1",
top:"8",
width:"100"
},
enabled:true,
onActionMethodID:"0E1D67B9-0909-4ACC-A42D-F5B1CEF1F8F8",
styleClass:"btn btn-default button_bts",
tabSeq:4,
text:"i18n:avanti.lbl.moveDown",
visible:true
},
name:"component_B685C7C5",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"E31F28ED-93C5-4327-8C9A-927A0B48D9AF"
},
{
height:250,
partType:5,
typeid:19,
uuid:"EB381EA6-6B91-438A-B01F-AD0729C7A2AB"
}
],
name:"sys_batch_inv_file_field_tbl",
navigatorID:"-1",
onShowMethodID:"D859FEFE-4D33-4E63-981C-6BCBEF0CBC35",
onSortCmdMethodID:"5069AEC1-1CEC-4A40-B17C-8C27B26E3662",
paperPrintScale:100,
scrollbars:33,
size:"1001,585",
styleName:null,
typeid:3,
uuid:"171648F8-8AF9-48B3-8063-DFE094B29A15",
view:0