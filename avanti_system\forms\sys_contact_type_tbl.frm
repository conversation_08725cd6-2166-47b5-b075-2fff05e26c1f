customProperties:"methods:{\
onShowMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
dataSource:"db:/avanti/sys_contact_type",
extendsID:"974D124E-73FF-475A-BF64-A1B3440FD00A",
items:[
{
cssPosition:"31,1,-1,1,690,35",
json:{
containedForm:"70FCE93B-8D70-4966-B9C1-F28CBF68D584",
cssPosition:{
bottom:"-1",
height:"35",
left:"1",
right:"1",
top:"31",
width:"690"
},
visible:true
},
name:"tabs_230",
typeName:"servoycore-formcontainer",
typeid:47,
uuid:"02512A12-9658-4F1C-8086-843051DC2FFE"
},
{
height:250,
partType:5,
typeid:19,
uuid:"591833D4-8E94-4EB0-82B3-A1C7DF8F11EB"
},
{
height:70,
partType:1,
typeid:19,
uuid:"668FFFB5-BF8F-4032-B5C4-58FD16CF275B"
},
{
height:255,
partType:8,
typeid:19,
uuid:"850BB76C-EB92-4B6D-9A5E-06B598F36278"
},
{
cssPosition:"5,0,-1,0,698,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"0",
right:"0",
top:"5",
width:"698"
},
enabled:true,
styleClass:"group_heading label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.contactTypeTableView",
visible:true
},
name:"lblHeader",
styleClass:"group_heading label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"B075F4E0-9DC3-4746-AB43-BFDE5509C826"
},
{
anchors:15,
cssPosition:"70px,0px,5px,0px,698px,180px",
json:{
anchors:15,
columns:[
{
autoResize:false,
dataprovider:null,
editType:null,
enableResize:false,
enableRowGroup:false,
enableSort:false,
enableToolPanel:false,
filterType:null,
format:null,
headerStyleClass:"text-right",
headerTitle:" ",
id:"btn_template0",
maxWidth:20,
minWidth:20,
rowGroupIndex:-1,
styleClass:"material-symbols-outlined arrow_right",
svyUUID:"11A78E64-8E47-4AE1-84CA-C39C9A0DF9A0",
valuelist:null,
visible:true,
width:20
},
{
autoResize:false,
dataprovider:"contacttype_desc",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.contactTypeDesc",
id:"contacttype_desc",
maxWidth:260,
minWidth:260,
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"8F8FB7B0-BD35-438C-B9F6-A12F1115F1D8",
valuelist:null,
visible:true,
width:260
},
{
autoResize:false,
dataprovider:"contacttype_workflow_type",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.workflowType",
id:"contacttype_workflow_type",
maxWidth:150,
minWidth:150,
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"6BF889F6-14B1-435E-9A33-2245829A3C15",
valuelist:"A0FB9C4C-2DD1-440B-8DDA-4B4AD36145C8",
visible:true,
width:150
}
],
cssPosition:{
bottom:"5px",
height:"180px",
left:"0px",
right:"0px",
top:"70px",
width:"698px"
},
enableColumnMove:true,
enableColumnResize:true,
enableSorting:true,
myFoundset:{
foundsetSelector:""
},
onCellClick:"14CDC582-4F3F-4764-BC98-2EDC55AFD5B0",
onCellDoubleClick:"8DFB9D7A-DB29-46DC-9299-79B1DBCBD41D",
onReady:"09D581E0-6B09-4500-93BF-11FB26F9804E",
responsiveHeight:300,
rowHeight:22,
styleClass:"ag-theme-alpine",
toolPanelConfig:{
suppressColumnExpandAll:false,
suppressColumnFilter:true,
suppressColumnSelectAll:false,
suppressRowGroups:false,
suppressSideButtons:false,
svyUUID:"6A813363-BD49-4068-A559-0C2085EF3B8B"
},
visible:true
},
name:"grid",
styleClass:"ag-theme-alpine",
typeName:"aggrid-groupingtable",
typeid:47,
uuid:"DE8E8AB7-2B31-47B0-8D45-123B350F06DA"
}
],
name:"sys_contact_type_tbl",
navigatorID:"-1",
onShowMethodID:"01FC5F63-402C-4C99-A859-98C86DDD722A",
scrollbars:33,
size:"698,480",
styleName:null,
typeid:3,
uuid:"F47552D5-93A6-4869-8663-196DD153BFF7",
view:0