customProperties:"methods:{\
onShowMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
dataSource:"db:/avanti/sa_price_type",
extendsID:"B37574A5-EFBD-46AC-994A-23C6ABB7D105",
initialSort:"pricetype_desc asc",
items:[
{
height:296,
partType:5,
typeid:19,
uuid:"0F2DCCF9-191D-41EB-8B6D-8E4849FD50CE"
},
{
cssPosition:"70,-1,-1,6,100,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"6",
right:"-1",
top:"70",
width:"100"
},
enabled:true,
labelFor:"avBase_selectedPriceType",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.priceType",
visible:true
},
name:"avBase_selectedPriceType_label",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"264A2637-76F5-471D-978F-20F7A1E677E5"
},
{
anchors:15,
cssPosition:"100,0,5px,0,1000px,195",
json:{
anchors:15,
columns:[
{
autoResize:false,
dataprovider:null,
editType:null,
enableResize:false,
enableRowGroup:false,
enableSort:false,
enableToolPanel:false,
filterType:null,
format:null,
headerStyleClass:"text-right",
headerTitle:" ",
id:"btn_template0",
maxWidth:20,
minWidth:20,
rowGroupIndex:-1,
styleClass:"material-symbols-outlined arrow_right",
svyUUID:"A3AEC74D-BD2A-406E-B4C1-2C8E4E429CAB",
valuelist:null,
visible:true,
width:20
},
{
autoResize:false,
dataprovider:"pricetype_desc",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.priceTypeDesc",
id:"pricetype_desc",
maxWidth:300,
minWidth:300,
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"CDECCAB8-15E8-4F6E-A9BE-CB2CF68BD84A",
valuelist:null,
visible:true,
width:300
},
{
autoResize:false,
dataprovider:"pricetype_segment_desc",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.priceSegments",
id:"pricetype_segment_desc",
maxWidth:660,
minWidth:660,
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"D660A35D-2E64-42F0-82FE-1CE24E2C8125",
valuelist:null,
visible:true,
width:660
}
],
cssPosition:{
bottom:"5px",
height:"195",
left:"0",
right:"0",
top:"100",
width:"1000px"
},
enableColumnMove:true,
enableColumnResize:true,
enableSorting:true,
myFoundset:{
foundsetSelector:""
},
onCellClick:"5E1D97D0-E598-4082-9B28-2C8B34E18F7D",
onCellDoubleClick:"0EFD86B8-2F39-4346-BC9E-8931516F4D9F",
onReady:"BBC4945D-4EDD-444B-AC74-C9F01FE4B3D7",
responsiveHeight:300,
rowHeight:22,
styleClass:"ag-theme-alpine",
toolPanelConfig:{
suppressColumnExpandAll:false,
suppressColumnFilter:true,
suppressColumnSelectAll:false,
suppressRowGroups:false,
suppressSideButtons:false,
svyUUID:"879BF81B-6F77-470F-AC76-3A6E73C72810"
},
visible:true
},
name:"grid",
styleClass:"ag-theme-alpine",
typeName:"aggrid-groupingtable",
typeid:47,
uuid:"3B49C872-A75A-4BA3-86CA-5B865DF765C4"
},
{
cssPosition:"70,-1,-1,110,160,24",
json:{
cssPosition:{
bottom:"-1",
height:"24",
left:"110",
right:"-1",
top:"70",
width:"160"
},
dataProviderID:"globals.avBase_selectedPriceType",
enabled:true,
onDataChangeMethodID:"7B8BC0F9-C4D7-4DA4-A0B2-C4B592825EC7",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"B09E0486-8120-47EC-AFA4-AC4B42283C8D",
visible:true
},
name:"avBase_selectedPriceType",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"602CC2A3-8CE9-4881-B218-6DDED7C7BE1D"
},
{
height:301,
partType:8,
typeid:19,
uuid:"7FC46D1D-58CF-4135-A32B-34EE7E12A807"
},
{
cssPosition:"30,-1,-1,1,950,35",
json:{
containedForm:"70FCE93B-8D70-4966-B9C1-F28CBF68D584",
cssPosition:{
bottom:"-1",
height:"35",
left:"1",
right:"-1",
top:"30",
width:"950"
},
visible:true
},
name:"tabs_230",
typeName:"servoycore-formcontainer",
typeid:47,
uuid:"8E4341EF-3559-404D-B587-965873877B33"
},
{
height:96,
partType:1,
typeid:19,
uuid:"A431180F-E1D9-477A-9888-B28731DDF23C"
},
{
cssPosition:"5,5,-1,1,994,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"1",
right:"5",
top:"5",
width:"994"
},
enabled:true,
styleClass:"group_heading label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.priceType_tableView",
visible:true
},
name:"lblHeader",
styleClass:"group_heading label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"AD6FFACB-9BDD-4DFF-8FFD-F5C1220D6F82"
}
],
name:"sa_price_type_tbl",
navigatorID:"-1",
onShowMethodID:"7C1B700A-871D-4F54-8F84-EDA876A80E4A",
scrollbars:33,
size:"1000,228",
styleName:null,
typeid:3,
uuid:"305AD401-C751-48E7-A8B5-EE448580CA89",
view:0