/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"8B4463A3-C7CC-4451-A981-34468820099F",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"AD10B79F-3E88-4A61-AF59-005291000B56"}
 */
function onReady() {
    _gridReady = 1;
};

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"183B7F67-7ACB-4658-8B66-F0CBC0D2D5AA"}
 */
function onShow(firstShow, event) {

	if (firstShow) {
		if (!_gridReady) {
			application.executeLater(onShow, 500, [true, event]);
			return null;
		}
	}

	// need this set to edit in order to be able to select chkbox
	globals.avUtilities_setFormEditMode(controller.getName(),"edit");
	foundset.addFoundSetFilterParam('is_hidden', '^||=', 0, 'notHiddenProjects');
    foundset.loadAllRecords();
    foundset.sort('custproj_desc asc');
    _super.onShowForm(firstShow, event);
}

/**
 * Called when the mouse is clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"CB2CC8D2-AB1B-46FA-88B4-7635A490034F"}
 */
function onCellClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "btn_template0") {
		scopes.globals.svy_nav_toggleView(event);
	}
	if (col.id == "sort_icon") {
		scopes.globals.avUtilities_shuffle(event);
	}
}

/**
 * Called when the mouse is double clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @SuppressWarnings(unused)
 * @properties={typeid:24,uuid:"03516FBC-48D8-4081-90B9-DAC293532593"}
 */
function onCellDoubleClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell right click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	scopes.globals.svy_nav_toggleView(event)
}

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"A026AF0A-6B84-4C80-891A-1AAE89262041",variableType:4}
 */
var _nSelectAll = 0;

/**
 * Handle changed data.
 *
 * @param {Number} oldValue old value
 * @param {Number} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @private
 *
 * @properties={typeid:24,uuid:"B002BF10-06C9-456D-99E1-70B0FF6714F8"}
 */
function onDataChange_seelctAll(oldValue, newValue, event) {
	scopes.avDB.updateFS(foundset, ['is_selected'], [newValue]);
	
	return true;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @private
 *
 * @properties={typeid:24,uuid:"5C60B4B0-A2EC-4C34-AE0F-56B870AD3396"}
 */
function onAction_deleteSelcted(event) {
	var sMsg = scopes.avText.getDlgMsg('deleteAllSelected?') + i18n.getI18NMessage('i18n:avanti.program.projects') + '?';
	
	if(scopes.avText.showYesNoQuestion(sMsg, true) == scopes.avText.yes){
		databaseManager.saveData(foundset);
		scopes.avDB.deleteRecs('sa_customer_project', ['is_selected'], [1]);
		_nSelectAll = 0;
	}
}
