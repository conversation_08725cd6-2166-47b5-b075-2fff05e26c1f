customProperties:"methods:{\
onShowMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
dataSource:"db:/avanti/sys_chargeback_file_group_fiel",
extendsID:"BD3226CB-C29A-4CAE-A46B-646C3FE287B0",
initialSort:"sequence_nr asc",
items:[
{
height:41,
partType:1,
typeid:19,
uuid:"04383BF1-6584-4A64-A635-0FC745C9CAC5"
},
{
anchors:15,
cssPosition:"41px,0px,5px,0px,1001px,209px",
json:{
anchors:15,
columns:[
{
autoResize:true,
dataprovider:"sequence_nr",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.columnOrder",
id:"fldColOrder",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"60DACCFA-9613-4AC8-9DA2-5D7F8B0BD374",
valuelist:null,
visible:true,
width:107
},
{
autoResize:true,
dataprovider:"fixed_position",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:"#,###|#,###",
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.position",
id:"fldPosition",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"CFE0E94E-3668-4F7C-99E9-DE7FD09B4A0F",
valuelist:null,
visible:true,
width:107
},
{
autoResize:true,
dataprovider:"fixed_length",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:"#,###|#,###",
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.length2",
id:"fldLength",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"55B257E0-3BF5-479C-9B3C-C3ED1056843C",
valuelist:null,
visible:true,
width:107
},
{
autoResize:true,
dataprovider:"acbf_id",
editType:"TYPEAHEAD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.avantiFieldName",
id:"fldFieldName",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"D586AF01-0BE8-47D8-830E-4214FD222EAB",
valuelist:"273A3C84-1F36-4A55-B67F-4B444ED22D21",
visible:true,
width:236
},
{
autoResize:true,
dataprovider:"col_header",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.ColumnDescription",
id:"fldColHeader",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"52BB8B9A-90E0-4D24-AA69-76EC512131C1",
valuelist:null,
visible:true,
width:251
},
{
autoResize:false,
dataprovider:"suppress_record_if_zero",
editType:"CHECKBOX",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.SuppressRecordIfZero",
id:"suppress_record_if_zero",
isEditableDataprovider:"clc_onRender_suppressRecordIfZero_enabled",
maxWidth:154,
minWidth:154,
rowGroupIndex:-1,
styleClass:"text-center",
styleClassDataprovider:"clc_onRender_suppressRecordIfZero_style",
svyUUID:"F5AEE6C3-A803-4D4D-AD73-12D7E36B0AD6",
valuelist:null,
visible:true,
width:154
},
{
autoResize:false,
dataprovider:"use_udv",
editType:"CHECKBOX",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.useUDV",
id:"fldUseUDV",
maxWidth:154,
minWidth:154,
rowGroupIndex:-1,
styleClass:"text-center",
svyUUID:"C2462990-5ADD-48D1-A10E-7C49D9C2610C",
valuelist:null,
visible:true,
width:154
},
{
autoResize:true,
dataprovider:"user_defined_value",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.UserDefinedValue",
id:"fldUDV",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"FB3AC0BA-D996-43E6-AEDF-809B71CFDC9D",
valuelist:null,
visible:true,
width:236
}
],
cssPosition:{
bottom:"5px",
height:"209px",
left:"0px",
right:"0px",
top:"41px",
width:"1001px"
},
enableColumnMove:true,
enableColumnResize:true,
enableSorting:true,
myFoundset:{
foundsetSelector:""
},
onCellClick:"93581CE8-A92A-42F5-A5D3-360C30571FFF",
onColumnDataChange:"F03C5BB6-635B-4CD0-BB76-224AE4E9200E",
onReady:"182AFDFE-81C7-44A4-B4D5-16CBACF1E567",
responsiveHeight:300,
rowHeight:22,
styleClass:"ag-theme-alpine",
toolPanelConfig:{
suppressColumnExpandAll:true,
suppressColumnFilter:true,
suppressColumnSelectAll:true,
suppressRowGroups:true,
suppressSideButtons:true,
svyUUID:"55A2DAF8-E798-4A5E-911B-189304BC0E97"
},
visible:true
},
name:"grid",
styleClass:"ag-theme-alpine",
typeName:"aggrid-groupingtable",
typeid:47,
uuid:"2D45335F-B313-4E75-B6C5-7278602684A1"
},
{
cssPosition:"8,-1,-1,39,25,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"39",
right:"-1",
top:"8",
width:"25"
},
enabled:true,
onActionMethodID:"6FA7349A-DDB9-4215-A680-1E5A020E5092",
styleClass:"listview_noborder label_bts",
tabSeq:2,
text:"%%globals.icon_trashCan%%",
visible:true
},
name:"btnDelete",
styleClass:"listview_noborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"3A47E5DE-83BC-4488-AA61-FFA495CC2DD3"
},
{
cssPosition:"8,-1,-1,184,100,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"184",
right:"-1",
top:"8",
width:"100"
},
enabled:true,
onActionMethodID:"3A7F03B7-D7F2-42FD-AC23-F7B72CE5CB3E",
styleClass:"btn btn-default button_bts",
tabSeq:4,
text:"i18n:avanti.lbl.moveDown",
visible:true
},
name:"btnDown",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"51A48013-8AE3-4D97-BE0B-08F8C454E93B"
},
{
cssPosition:"9,-1,-1,853,140,20",
json:{
appendToBody:true,
cssPosition:{
bottom:"-1",
height:"20",
left:"853",
right:"-1",
top:"9",
width:"140"
},
dataProviderID:"_sDetailHeaderType",
enabled:true,
onDataChangeMethodID:"D97CD4A1-923A-4FEE-AD27-202E03CBD9FD",
showAs:"text",
styleClass:"typeahead_bts",
tabSeq:0,
valuelistID:"BEE1B2E7-EE8D-4EE0-A267-061E4CF4990B",
visible:true
},
name:"fldDetailHeaderType",
styleClass:"typeahead_bts",
typeName:"bootstrapcomponents-typeahead",
typeid:47,
uuid:"58CF768F-C742-48B4-A8E9-BCF1D16EF499"
},
{
cssPosition:"8,-1,-1,296,118,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"296",
right:"-1",
top:"8",
width:"118"
},
enabled:true,
onActionMethodID:"1DBA8D48-DD82-42E8-A771-E760CB628A08",
styleClass:"btn btn-default button_bts",
tabSeq:5,
text:"i18n:avanti.lbl.ClearPositions",
visible:true
},
name:"btnClearPositions",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"5E6E9FA0-75E9-45B0-959C-9B812A90A7A7"
},
{
cssPosition:"9,-1,-1,629,218,20",
json:{
cssPosition:{
bottom:"-1",
height:"20",
left:"629",
right:"-1",
top:"9",
width:"218"
},
enabled:true,
labelFor:"fldDetailHeaderType",
styleClass:"label_bts",
tabSeq:-1,
text:"i18n:avanti.lbl.CreateDetailHeaderRecordBy",
visible:true
},
name:"lblDetailHeaderType",
styleClass:"label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"9FA7FF83-BEB1-4A39-95D4-3EAB1C79DE61"
},
{
height:255,
partType:8,
typeid:19,
uuid:"A3797EA6-B304-46B7-8C4D-D56C6E786C30"
},
{
cssPosition:"8,-1,-1,9,25,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"9",
right:"-1",
top:"8",
width:"25"
},
enabled:true,
onActionMethodID:"A47E8065-1A27-49E6-8FDF-82E5B9999D04",
styleClass:"listview_noborder label_bts",
tabSeq:1,
text:"%%globals.icon_add%%",
visible:true
},
name:"btnAdd",
styleClass:"listview_noborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"A4EC1D83-4D3E-4404-935B-9DEC631C66D1"
},
{
height:250,
partType:5,
typeid:19,
uuid:"BA4F95C7-9ADB-41EA-8440-5F88BEC7F4FD"
},
{
cssPosition:"8,-1,-1,74,100,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"74",
right:"-1",
top:"8",
width:"100"
},
enabled:true,
onActionMethodID:"B4517F90-9338-4269-9A66-DCD1A27F840F",
styleClass:"btn btn-default button_bts",
tabSeq:3,
text:"i18n:avanti.lbl.moveUp",
visible:true
},
name:"btnUp",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"C88EF808-2EAC-4C40-BD2B-E8B9C1C0D03D"
}
],
name:"sys_chargeback_file_detail_header_tbl",
navigatorID:"-1",
onShowMethodID:"E238F17A-6795-44EA-89F5-5077A2845FCA",
onSortCmdMethodID:"ED836EC2-E12B-4394-ACD2-CD4202C40710",
paperPrintScale:100,
scrollbars:33,
size:"1001,585",
styleName:null,
typeid:3,
uuid:"DF93C303-F9BF-4950-8A3C-6DA6A8F85A3E",
view:0