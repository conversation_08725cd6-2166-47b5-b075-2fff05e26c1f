/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"58079EE2-3AE6-4297-9AE2-EEEFD2DAAE11",variableType:-4}
 */
var aArrayOfAccountingLinks = ['cc_sales_glacctseg_id','cc_glacctseg_id_wip','cc_fg_glacctseg_id','cc_cogs_glacctseg_id','cc_base_rate_gl','cc_labour_gl','cc_overhead_gl'];

/** *
 * @param _event
 * @param _triggerForm
 *
 * @properties={typeid:24,uuid:"E188CA15-EBDC-42A6-904E-824F24777B18"}
 * @AllowToRunInFind
 */
function dc_delete(_event, _triggerForm)
{
	// Make sure the detail form has the same record
	if (globals.svy_nav_form_name == "sys_cost_center_tbl")
	{
		forms.sys_cost_center_dtl.controller.loadRecords(forms.sys_cost_center_tbl.foundset);
		
		globals.avUtilities_object = new Object();
		globals.avUtilities_object["iDeleteRunning"] = 1;
		
		globals.avUtilities_delete(_event,true,true);
		
		globals.avUtilities_object = null;
		
		// refresh the value list
		globals.avUtilities_setVL_costCenters();
	}
	else
	{
		globals.DIALOGS.showInfoDialog(i18n.getI18NMessage("avanti.dialog.deleteCostCenter_title"), 
			i18n.getI18NMessage("avanti.dialog.deleteCostCenter_msg"), 
			i18n.getI18NMessage("avanti.dialog.ok"));

		globals.svy_nav_showForm('sys_cost_center_tbl','Cost_Center');
	}

//	_super.dc_delete(_event, _triggerForm);
	

}

/** *
 * @param _event
 * @param _triggerForm
 *
 * @properties={typeid:24,uuid:"397D7368-5A5B-4888-ADDA-FAB6E2CD7111"}
 */
function dc_duplicate(_event, _triggerForm)
{
	// Make sure the detail form has the same record
	if (globals.svy_nav_form_name == "sys_cost_center_tbl")
	{
		forms.sys_cost_center_dtl.controller.loadRecords(forms.sys_cost_center_tbl.foundset);
	}
	
	_super.dc_duplicate(_event, _triggerForm);
	
	if (foundset.getSize() > 0)
	{
		var rRec = foundset.getSelectedRecord();
		rRec.cc_desc += i18n.getI18NMessage("avanti.lbl.duplicated");
		
		globals.avBase_selectedCostCenterID = rRec.cc_id;
		globals.avBase_selectedDeptID = rRec.dept_id;
		
		forms.sys_cost_center_dtl.controller.loadRecords(foundset);
		forms.sys_cost_center_tbl_listView.controller.loadRecords(foundset);
	}
	
}

/**
 *
 * @param {JSEvent} _event
 * @param {String} _triggerForm
 *
 * @return
 * @properties={typeid:24,uuid:"DAE307A1-1B19-49E8-AD5F-85FE4981FFC7"}
 */
function dc_edit(_event, _triggerForm) {
    var result = _super.dc_edit(_event, _triggerForm);
    scopes.avSecurity.updateEditableFields('sys_cost_center_dtl', 'bgcolor', aArrayOfAccountingLinks);
    return result;
}

/**
*
* @param {JSEvent} _event
* @param {String} _triggerForm
*
 * @return
* @properties={typeid:24,uuid:"ACAAAEAB-3D59-4F3E-9750-509767460EE6"}
*/
function dc_new(_event, _triggerForm) {
   var result = _super.dc_new(_event, _triggerForm);
   scopes.avSecurity.updateEditableFields('sys_cost_center_dtl', 'bgcolor', aArrayOfAccountingLinks);
   return result;
}

/**
 * @param {Boolean} _firstShow
 * @param {JSEvent} _event
 * @override
 *
 * @properties={typeid:24,uuid:"9D398505-0497-42B5-9759-************"}
 */
function onShowForm(_firstShow, _event) {
	return _super.onShowForm(_firstShow, _event)
}
