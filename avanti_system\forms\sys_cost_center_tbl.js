/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"DE0D4F30-74C3-4C5F-8CF9-EB082FE5477F",variableType:4}
 */
var _gridReady = 0;

/**
 * Called when the table is ready to be shown.
 *
 * @protected
 *
 *
 * @properties={typeid:24,uuid:"EBBDF477-B7CB-4FAF-A0C0-13579F7D7D21"}
 */
function onReady() {
    _gridReady = 1;
};

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"67893720-0ED2-4096-B30E-5DD244FB2E95"}
 * @AllowToRunInFind
 */
function onShow(firstShow, event)
{

	if (firstShow) {
		if (!_gridReady) {
			application.executeLater(onShow, 500, [true, event]);
			return null;
		}
	}
	
	if (!globals.avBase_selectedPlantID && _onShowRunning == 0)
	{
		// Set it to a default
		var dsPlant = application.getValueListItems("vl_Plants");
		if (dsPlant.getMaxRowIndex() > 0)
		{
			globals.avBase_selectedPlantID = dsPlant.getValue(1, 2);
			globals.avBase_selectedDivID = _to_sys_plant$avbase_selected_plant_id.div_id;
		}
	}
	else
	{
		// Load the records for this division
		if (globals.avBase_selectedPlantID && _onShowRunning == 0)
		{
			// Load the records
			refreshUI();

			if (globals.avBase_selectedCostCenterID)
			{
				if (foundset.find() || foundset.find())
				{
					foundset.cc_id = globals.avBase_selectedCostCenterID;
					foundset.search(false, false);
					foundset.selectRecord(globals.avBase_selectedCostCenterID);
					elements.grid.myFoundset.foundset.loadRecords(foundset);
				}
			}
		}
	}

	// Only run the methods here once
	if (_onShowRunning == 0)
	{
		_onShowRunning = 1;
	}
	else
	{
		_onShowRunning = 0;
	}
	
	_super.onShowForm(firstShow, event);
}

/**
 * Called when the mouse is clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @properties={typeid:24,uuid:"FC3DE7AE-2F8D-49B6-ABCD-8E387D4B1417"}
 */
function onCellClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	if (col.id == "btn_template0") {
		btnSelect(event);
	}
}

/**
 * Called when the mouse is double clicked on a row/cell (foundset and column indexes are given).
 * the foundsetindex is always -1 when there are grouped rows
 *
 * @param {Number} foundsetindex
 * @param {Number} columnindex
 * @param {JSRecord} record
 * @param {JSEvent} event
 *
 * @private
 *
 * @SuppressWarnings(unused)
 * @properties={typeid:24,uuid:"6A570153-2499-4078-A832-C7985F92EA46"}
 */
function onCellDoubleClick(foundsetindex, columnindex, record, event) {
	foundset.setSelectedIndex(foundsetindex);
	if(columnindex < 0) {
		application.output("Cell right click not handled in group mode", LOGGINGLEVEL.WARNING);		return;
	}
	
	/** @type {CustomType<aggrid-groupingtable.column>} */
	var col = elements[event.getElementName()].getColumn(columnindex);
	
	btnSelect(event)
}

/**
 * @properties={typeid:35,uuid:"26AD9036-6898-4D3A-9412-21AE27A32F5F",variableType:-4}
 */
var _oFoundset = null;

/**
 * @type {Number}
 *
 * @properties={typeid:35,uuid:"333C1CCD-E558-429E-82B5-DDA5BEF2C610",variableType:4}
 */
var _onShowRunning = 0;

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"3C5DEBBD-1DAF-477A-ADBB-D73D06018D49"}
 */
function btnSelect(event)
{
	_oFoundset = foundset;
	
	var rRec = foundset.getSelectedRecord();
	globals.avBase_selectedCostCenterID = rRec.cc_id;
	globals.avBase_selectedDeptID = rRec.dept_id;
	globals.avBase_selectedPlantID = rRec.plant_id;
	globals.avBase_selectedOpCatID = rRec.opcat_id;
	globals.avBase_selectedOpID = rRec.op_id;
	
	forms.sys_cost_center_dtl.controller.loadRecords(foundset);
	forms.sys_cost_center_tbl_listView.controller.loadRecords(foundset);
	globals.svy_nav_toggleView(event, "sys_cost_center_dtl", "btn_template0");
}

/**
 * Handle changed data.
 *
 * @param {Object} oldValue old value
 * @param {Object} newValue new value
 * @param {JSEvent} event the event that triggered the action
 *
 * @returns {Boolean}
 *
 * @properties={typeid:24,uuid:"D2A8CA61-7494-497A-9022-E2C6A220A02B"}
 */
function onDataChange_plantID(oldValue, newValue, event)
{
	if (oldValue != newValue)
	{
		globals.avBase_selectedPlantID = newValue;
		globals.avBase_selectedDivID = _to_sys_plant$avbase_selected_plant_id.div_id
		refreshUI();
	}
	return true
}

/**
 * Refresh the UI
 *
 * <AUTHOR> Dotzlaw
 * @since 2011-05-30
 *
 * @properties={typeid:24,uuid:"FB540F1A-1FA3-42D3-B594-C49DEF9B04B5"}
 * @AllowToRunInFind
 */
function refreshUI()
{
	if (_oFoundset) controller.loadRecords(_oFoundset);
	
	var fsCC = foundset;
	if (fsCC.find() || fsCC.find()) {
		fsCC.plant_id = globals.avBase_selectedPlantID;
		fsCC.search();
	}
	elements.grid.myFoundset.foundset.loadRecords(fsCC);
	return;
}

/** *
 * @param _event
 * @param _triggerForm
 *
 * @return
 * @properties={typeid:24,uuid:"D23B3B8A-168D-4361-AF77-C598F030A328"}
 */
function dc_new(_event, _triggerForm)
{
	if (!globals.avBase_selectedPlantID)
	{
		// Throw error dialog; Plants need to be created first before Cost Centers
		globals.DIALOGS.showErrorDialog(i18n.getI18NMessage("avanti.dialog.createCostCenter_title"), 
			i18n.getI18NMessage("avanti.dialog.createCostCenter_msg"), 
			i18n.getI18NMessage("avanti.dialog.ok"));
		return false;
	}
	
	globals.avBase_selectedCostCenterID = cc_id;
	forms.sys_cost_center_dtl.controller.loadRecords(foundset);
	forms.sys_cost_center_tbl_listView.controller.loadRecords(foundset);
	
	_super.dc_new(_event,  _triggerForm);
	
	var rRec = foundset.getSelectedRecord();
	//Setting the default lag time fields for the cost center
	rRec.cc_dflt_lag_type = 'FS';
	rRec.cc_dflt_lag = 0;
	rRec.cc_dflt_lag_units = 'm';
	
	globals.svy_nav_toggleView(_event, "sys_cost_center_dtl", "btn_template0");
	
	forms.sys_cost_center_tbl_listView.foundset.selectRecord(rRec.cc_id);
	forms.sys_cost_center_tbl_listView.onRecordSelection(null, null, true);
	
	return true;
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"A3A555C9-8ECD-4DB7-8071-C46AD472799F"}
 */
function onAction_PlantLookup(event)
{
	var oParams = new Object();
	oParams.mode = "lookup";
	globals.svy_nav_showLookupWindow(event, "fldPlantID", "Plants", "btnSelectReturn", null, oParams);

}

/** *
 * @param _event
 * @param _triggerForm
 * @param _answer
 *
 * @properties={typeid:24,uuid:"616353B6-5E64-4125-9507-069F458E2E05"}
 */
function dc_cancel(_event, _triggerForm, _answer)
{
	_super.dc_cancel(_event, _triggerForm, _answer);
	
	// Set the QuickSearch form back into editable mode
	forms.utils_quickSearch.controller.readOnly = false;
}

/** *
 * @param _event
 * @param _triggerForm
 *
 * @properties={typeid:24,uuid:"DBC324C8-8EC8-4400-8905-4E068E38040B"}
 */
function dc_save(_event, _triggerForm)
{
	_super.dc_save(_event, _triggerForm);
	
	// Set the QuickSearch form back into editable mode
	forms.utils_quickSearch.controller.readOnly = false;
}
