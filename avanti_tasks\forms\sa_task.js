/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"7D6ABA33-FCAC-4246-A25F-739CCB64059F",variableType:-4}
 */
var aArrayOfAccountingLinksPostage = ['taskstd_glacct_id_sales', 'taskstd_glacct_id_postage_exp', 'taskstd_glacct_id_postage_rev', 
    'taskstd_glacct_id_postage_clr'];

/**
 * @type {Array<String>}
 *
 * @properties={typeid:35,uuid:"FC8D5A33-3014-40F8-AECE-34F92CB92086",variableType:-4}
 */
var aArrayOfAccountingLinksOutsourced = ['taskstd_glacct_id_sales', 'taskstd_glacct_id_costsales', 'taskstd_glacct_id_wip', 'taskstd_glacct_id_fg'];

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"3918CB67-F904-44AB-A02A-CCF5AF9B9550"}
 */
var _calcType = 'I';

/**
 * @type {String}
 *
 * @properties={typeid:35,uuid:"B6716E8F-0929-4999-A7BC-CCF1E734953A"}
 */
var _taskForm = '';

/** *
 * @param _event
 * @param _triggerForm
 *
 * @return
 * @properties={typeid:24,uuid:"1149F5E1-F0D7-471B-9AB1-F039AFB2118C"}
 */
function dc_delete(_event, _triggerForm) {
    var sForm = globals.svy_nav_form_name;

    /***@type{JSRecord<db:/avanti/sa_task>}*/
    var rRec = forms[sForm].foundset.getSelectedRecord();

    if (rRec && rRec.task_is_sys_standard == 1 && globals.avBase_developmentMode != 1) {
        globals.DIALOGS.showInfoDialog(i18n.getI18NMessage("avanti.dialog.taskStandard_title"),
            i18n.getI18NMessage("avanti.dialog.taskStandard_msg"),
            i18n.getI18NMessage("avanti.dialog.ok"));

        return i18n.getI18NMessage('avanti.dialog.cancel');
    }

    if (rRec.task_is_system == 1) //(rRec.tasktype_id == 99 || rRec.tasktype_id == 98 || rRec.tasktype_id == 97 || rRec.tasktype_id == 5) // User cannot delete these
    {
        globals.DIALOGS.showInfoDialog(i18n.getI18NMessage("avanti.dialog.deleteSystemTaskType_title"), i18n.getI18NMessage("avanti.dialog.deleteSystemTaskType_msg"), i18n.getI18NMessage("avanti.dialog.ok"));

        return i18n.getI18NMessage('avanti.dialog.cancel');
    }

    return _super.dc_delete(_event, _triggerForm);
}

/**
 * Callback method for when form is shown.
 *
 * @param {Boolean} firstShow form is shown first time after load
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"88924BE9-8211-4611-914D-C72609BA2D8E"}
 */
function onShow(firstShow, event) {
    var sForm = event.getFormName();

    if (sForm == "sa_task_tbl") {
        // GD - 2014-02-22: Arron had me take out (clc_task_type_description asc,)
        // GD - 2012-11-10: Default sort order for this view
        if (foundset.getCurrentSort() == "task_id asc" || foundset.getCurrentSort() == null || foundset.getCurrentSort() == "") {
            foundset.sort(scopes.avTask.sortTasks);
        }
    }
    else {
        foundset.sort("task_description asc");
    }
    _super.onShowForm(firstShow, event);
}

/**
 *
 * @param {JSEvent} _event
 * @param {String} _triggerForm
 *
 * @return
 * @properties={typeid:24,uuid:"7EAE5D83-BB93-4FA2-BBF6-F44AB2A6BE7A"}
 */
function dc_edit(_event, _triggerForm) {
    var result = _super.dc_edit(_event, _triggerForm);
    switch (globals.avBase_selectedTaskTypeID) {
        case scopes.avTask.TASKTYPEID.Postage:
            scopes.avSecurity.updateEditableFields('sa_task_standards_postage_accounting_links', 'bgcolor', aArrayOfAccountingLinksPostage);
            break
        case scopes.avTask.TASKTYPEID.OutsideService:
            scopes.avSecurity.updateEditableFields('sa_task_standards_outsourced_accounting_links', 'bgcolor', aArrayOfAccountingLinksOutsourced);
            break;
        default:
            break;
    }
    return result;
}

/**
 *
 * @param {JSEvent} _event
 * @param {String} _triggerForm
 *
 * @return
 * @properties={typeid:24,uuid:"4A71AFC6-6E35-4A71-8DFA-E1964686C2E3"}
 */
function dc_new(_event, _triggerForm) {
    var result = _super.dc_new(_event, _triggerForm);
    switch (globals.avBase_selectedTaskTypeID) {
        case scopes.avTask.TASKTYPEID.Postage:
            scopes.avSecurity.updateEditableFields('sa_task_standards_postage_accounting_links', 'bgcolor', aArrayOfAccountingLinksPostage);
            break
        case scopes.avTask.TASKTYPEID.OutsideService:
            scopes.avSecurity.updateEditableFields('sa_task_standards_outsourced_accounting_links', 'bgcolor', aArrayOfAccountingLinksOutsourced);
            break;
        default:
            break;
    }
    return result;
}
