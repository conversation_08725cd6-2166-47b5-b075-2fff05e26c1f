
/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"37EEF227-32AB-4C88-9028-A8091B75EE23"}
 */
function onActionOK(event) {
	for(var fs_idx = 1; fs_idx <= foundset.getSize(); fs_idx++) {
		if(foundset.getRecord(fs_idx).qa_check_pos_1 && foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1_placehold == 0){
			globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
				i18n.getI18NMessage('avanti.dialog.shopFloorConfirmQAChecks_Resource'),
				i18n.getI18NMessage('avanti.dialog.ok'))
			return false
		}
		
		if(foundset.getRecord(fs_idx).qa_check_pos_2 && foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2_placehold == 0){
			globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
				i18n.getI18NMessage('avanti.dialog.shopFloorConfirmQAChecks_Resource'),
				i18n.getI18NMessage('avanti.dialog.ok'))
			return false
		}
		
		if(foundset.getRecord(fs_idx).qa_check_pos_3 && foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3_placehold == 0){
			globals.DIALOGS.showWarningDialog(i18n.getI18NMessage('avanti.lbl.confirmationRequired'),
				i18n.getI18NMessage('avanti.dialog.shopFloorConfirmQAChecks_Resource'),
				i18n.getI18NMessage('avanti.dialog.ok'))
			return false
		}
	}
	resetPlaceHolder()
	bHitOK=true
	return globals.DIALOGS.closeForm(null, 'QA Checks'); // GD - Apr 4, 2014: Just added a return to clear the jsDoc warning
}

/**
 * Perform the element default action.
 *
 * @param {JSEvent} event the event that triggered the action
 *
 * @properties={typeid:24,uuid:"5A5ED3B0-8A3C-4F73-B02C-9553B3A86DD9"}
 */
function onActionCancel(event) {
	resetPlaceHolder()
	bHitOK=false
	globals.DIALOGS.closeForm(event);
	return false
}

/**
 * @properties={typeid:24,uuid:"B2E174D2-585F-4634-A313-265E6CF4D254"}
 */
function resetPlaceHolder() {
	for(var fs_idx = 1; fs_idx <= foundset.getSize(); fs_idx++) {
		foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1_placehold = 0
		foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2_placehold = 0
		foundset.getRecord(fs_idx).sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3_placehold = 0
		
	}
}


/**
 * Called before the form component is rendered.
 *
 * @param {JSRenderEvent} event the render event
 *
 * @properties={typeid:24,uuid:"C972A95D-BAE0-46B3-A384-BDD103B04BA0"}
 */
function onRenderCheck1(event) {
	/***@type {JSRecord<db:/avanti/sch_milestone_group>} */
	var currentRowRecord = event.getRecord()
	var check_field = event.getRenderable()
	check_field.visible = true
	
	if(currentRowRecord && !currentRowRecord.qa_check_pos_1){
		check_field.visible = false
	}
}

/**
 * Called before the form component is rendered.
 *
 * @param {JSRenderEvent} event the render event
 *
 * @properties={typeid:24,uuid:"0C78265A-25E3-4E98-BAE3-A71FD9162BEC"}
 */
function onRenderCheck2(event) {
	/***@type {JSRecord<db:/avanti/sch_milestone_group>} */
	var currentRowRecord = event.getRecord()
	var check_field = event.getRenderable()
	check_field.visible = true

	if(currentRowRecord && !currentRowRecord.qa_check_pos_2){
		check_field.visible = false
	}
}

/**
 * Called before the form component is rendered.
 *
 * @param {JSRenderEvent} event the render event
 *
 * @properties={typeid:24,uuid:"B8401055-CB6F-49B4-84A9-35DDA2E9F709"}
 */
function onRenderCheck3(event) {
	/***@type {JSRecord<db:/avanti/sch_milestone_group>} */
	var currentRowRecord = event.getRecord()
	var check_field = event.getRenderable()
	
	check_field.visible = true
	if(currentRowRecord && !currentRowRecord.qa_check_pos_3){
		check_field.visible = false
	}
	
	

//	check_field.visible = false
//	if(currentRowRecord && currentRowRecord.qa_check_pos_3){
//		application.output('currentRowRecord.qa_check_pos_3: ' + currentRowRecord.qa_check_pos_3)
//		check_field.visible = true
//	}
	
}
