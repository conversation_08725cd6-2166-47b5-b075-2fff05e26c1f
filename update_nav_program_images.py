#!/usr/bin/env python3
"""
Script to batch update menuitem_image column in nav_menu_items.csv based on SQL update script
"""

import csv
import re

# Define the exact mapping from SQL script (36 updates total for nav_menu_items)
# Based on menu_item_id -> menuitem_image mappings
menu_item_image_updates = {
    '60': 'home',
    '62': 'inventory_2',
    '67': 'diversity_3',
    '68': 'settings',
    '103': 'domain',
    '156': 'manage_accounts',
    '206': 'account_tree',
    '216': 'login',
    '220': 'code',
    '239': 'lock',
    '253': 'manufacturing',
    '256': 'analytics',
    '259': 'diversity_3',
    '260': 'request_quote',
    '261': 'local_shipping',
    '262': 'account_tree',
    '263': 'groups',
    '264': 'box_add',
    '265': 'inventory_2',
    '266': 'receipt_long',
    '267': 'overview',
    '293': 'request_quote',
    '294': 'local_shipping',
    '298': 'receipt_long',
    '300': 'groups',
    '310': 'overview',
    '333': 'id_card',
    '338': 'rule_settings',
    '347': 'calculate',
    '361': 'folder',
    '426': 'mail',
    '455': 'list_alt',
    '474': 'list_alt',
    '542': 'all_inclusive',
    '574': 'box_add',
    '597': 'book_2'
}

def update_csv_file():
    """Update the nav_menu_items.csv file with new menuitem_image values"""
    csv_file_path = '!_avanti_artifacts\\slingshot_artifacts\\_svy_frmwrk_data\\nav_menu_items.csv'

    # Read the CSV file
    with open(csv_file_path, 'r', encoding='utf-8') as file:
        content = file.read()

    # Split into lines
    lines = content.strip().split('\n')

    # Process each line
    updated_lines = []
    updates_made = 0

    for line in lines:
        # Skip header line
        if line.startswith('"menu_item_id"'):
            updated_lines.append(line)
            continue

        # Parse CSV line (simple approach for this specific format)
        fields = line.split('"|"')

        if len(fields) >= 8:  # Ensure we have enough fields
            # menu_item_id is at index 0 (1st field)
            # menuitem_image is at index 7 (8th field)
            menu_item_id = fields[0].strip('~"')

            if menu_item_id in menu_item_image_updates:
                # Update the menuitem_image field
                old_image = fields[7]
                fields[7] = menu_item_image_updates[menu_item_id]
                updated_line = '"|"'.join(fields)
                updated_lines.append(updated_line)
                updates_made += 1
                print(f"Updated menu_item_id {menu_item_id}: {old_image} -> {menu_item_image_updates[menu_item_id]}")
            else:
                updated_lines.append(line)
        else:
            updated_lines.append(line)

    # Write the updated content back
    with open(csv_file_path, 'w', encoding='utf-8') as file:
        file.write('\n'.join(updated_lines))

    print(f"\nTotal updates made: {updates_made}")
    print(f"Expected: 36 updates")
    print("CSV file updated successfully!")

if __name__ == "__main__":
    update_csv_file()
