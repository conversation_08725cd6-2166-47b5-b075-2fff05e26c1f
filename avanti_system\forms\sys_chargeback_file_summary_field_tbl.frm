customProperties:"methods:{\
onShowMethodID:{\
arguments:null,\
parameters:null\
}\
},\
useCssPosition:true",
dataSource:"db:/avanti/sys_chargeback_file_sum_field",
extendsID:"BD3226CB-C29A-4CAE-A46B-646C3FE287B0",
initialSort:"sequence_nr asc",
items:[
{
anchors:15,
cssPosition:"41px,0px,5px,0px,1001px,209px",
json:{
anchors:15,
columns:[
{
autoResize:true,
dataprovider:"sequence_nr",
editType:null,
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.SummaryFieldOrder",
id:"fldColOrder",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"39B0AEA7-BB09-49BF-9859-3D4432484292",
valuelist:null,
visible:true,
width:151
},
{
autoResize:true,
dataprovider:"cbfsf_position",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:"#,###|#,###",
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.position",
id:"fldPosition",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"3F9AA969-DA72-42D1-95DF-040255B3E2A6",
valuelist:null,
visible:true,
width:107
},
{
autoResize:true,
dataprovider:"cbfsf_length",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:"#,###|#,###",
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.length2",
id:"fldLength",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"B5930BD0-B4EC-4245-9E9A-A2534C463A94",
valuelist:null,
visible:true,
width:107
},
{
autoResize:true,
dataprovider:"acbf_id",
editType:"TYPEAHEAD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.avantiFieldName",
id:"fldFieldName",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"20BBB9BD-0141-42EC-AD3C-0E36552CA1F0",
valuelist:"68388B77-DCBC-4463-B21D-140895444D26",
visible:true,
width:236
},
{
autoResize:true,
dataprovider:"cbfsf_summary_type",
editType:"COMBOBOX",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.SummaryType",
id:"fldSumType",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"71024D7F-F174-42EB-92B5-2534DF1DA231",
valuelist:"8E91D9E5-0543-4915-BAD8-CF48484E800F",
visible:true,
width:125
},
{
autoResize:true,
dataprovider:"cbfsf_col_header",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.columnHeader",
id:"fldColHeader",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"A80A2466-6DEA-4C82-B0D7-790698EA3BEE",
valuelist:null,
visible:true,
width:251
},
{
autoResize:false,
dataprovider:"cbfsf_use_udv",
editType:"CHECKBOX",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-center",
headerTitle:"i18n:avanti.lbl.useUDV",
id:"fldUseUDV",
maxWidth:154,
minWidth:154,
rowGroupIndex:-1,
styleClass:"text-center",
svyUUID:"FC78FC47-4ACA-4A10-A6FB-55EE7418ADB0",
valuelist:null,
visible:true,
width:154
},
{
autoResize:true,
dataprovider:"cbfsf_user_defined_value",
editType:"TEXTFIELD",
enableRowGroup:true,
enableSort:true,
filterType:"TEXT",
format:null,
headerStyleClass:"text-left",
headerTitle:"i18n:avanti.lbl.UserDefinedValue",
id:"fldUDV",
rowGroupIndex:-1,
styleClass:"text-left",
svyUUID:"5403D123-0E69-46C9-B1F0-DF3E801A7D54",
valuelist:null,
visible:true,
width:236
}
],
cssPosition:{
bottom:"5px",
height:"209px",
left:"0px",
right:"0px",
top:"41px",
width:"1001px"
},
enableColumnMove:true,
enableColumnResize:true,
enableSorting:true,
myFoundset:{
foundsetSelector:""
},
onCellClick:"AA945028-57E9-4B42-93AC-503ED824319E",
onColumnDataChange:"E393340D-590A-4893-B6F0-13496FC83294",
onReady:"AA40CBB0-00AD-4071-AC26-4DFA512AB0EC",
responsiveHeight:300,
rowHeight:22,
styleClass:"ag-theme-alpine",
toolPanelConfig:{
suppressColumnExpandAll:true,
suppressColumnFilter:true,
suppressColumnSelectAll:true,
suppressRowGroups:true,
suppressSideButtons:true,
svyUUID:"D1FF5E44-3F37-4F4A-A9BA-4F921864A18C"
},
visible:true
},
name:"grid",
styleClass:"ag-theme-alpine",
typeName:"aggrid-groupingtable",
typeid:47,
uuid:"1499CDA8-6D4B-4E35-8909-9278FB4B54E8"
},
{
cssPosition:"8,-1,-1,295,118,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"295",
right:"-1",
top:"8",
width:"118"
},
enabled:true,
onActionMethodID:"62D86883-782D-485A-BA1F-901C3989EB94",
styleClass:"btn btn-default button_bts",
tabSeq:5,
text:"i18n:avanti.lbl.ClearPositions",
visible:true
},
name:"btnClearPositions",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"18A91D8C-B151-4706-9AD1-7BCA8B871B88"
},
{
cssPosition:"8,-1,-1,74,100,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"74",
right:"-1",
top:"8",
width:"100"
},
enabled:true,
onActionMethodID:"535B0B63-E358-4EB6-942C-6607E5C68C31",
styleClass:"btn btn-default button_bts",
tabSeq:3,
text:"i18n:avanti.lbl.moveUp",
visible:true
},
name:"component_A66B212C",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"5384AC0C-ACE4-442B-B703-E049EE9D30BC"
},
{
cssPosition:"8,-1,-1,184,100,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"184",
right:"-1",
top:"8",
width:"100"
},
enabled:true,
onActionMethodID:"2A098851-881B-4EFF-8083-9F3DCE4923E4",
styleClass:"btn btn-default button_bts",
tabSeq:4,
text:"i18n:avanti.lbl.moveDown",
visible:true
},
name:"component_F92BB0CC",
styleClass:"btn btn-default button_bts",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"57A8B471-71B6-4352-A1D9-DDA23901A3BE"
},
{
height:41,
partType:1,
typeid:19,
uuid:"6CD9D7CE-6C01-4421-8E45-1E5450D4D502"
},
{
cssPosition:"8,-1,-1,39,25,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"39",
right:"-1",
top:"8",
width:"25"
},
enabled:true,
onActionMethodID:"F28DB481-B493-4C37-A8BF-88D344440484",
styleClass:"listview_noborder label_bts",
tabSeq:2,
text:"%%globals.icon_trashCan%%",
visible:true
},
name:"btnDelete",
styleClass:"listview_noborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"8A5D5B79-EA4D-4148-9A02-A4A50A029062"
},
{
height:250,
partType:5,
typeid:19,
uuid:"8D781E5D-5408-475B-A7B4-A449671BC9D6"
},
{
cssPosition:"8,-1,-1,9,25,22",
json:{
cssPosition:{
bottom:"-1",
height:"22",
left:"9",
right:"-1",
top:"8",
width:"25"
},
enabled:true,
onActionMethodID:"F7115615-938E-4D32-9717-D92F49B6D9CE",
styleClass:"listview_noborder label_bts",
tabSeq:1,
text:"%%globals.icon_add%%",
visible:true
},
name:"btnAdd",
styleClass:"listview_noborder label_bts",
typeName:"bootstrapcomponents-label",
typeid:47,
uuid:"8DD4CFA8-097E-4172-B672-82EC5E628DF5"
},
{
height:255,
partType:8,
typeid:19,
uuid:"FFDFD010-CE5B-4368-86EC-C93BD0AFF92A"
}
],
name:"sys_chargeback_file_summary_field_tbl",
navigatorID:"-1",
onShowMethodID:"DDA676CD-DFF2-4C4B-85A2-A7817B98E60A",
onSortCmdMethodID:"372986D4-0A4F-4347-9B98-D19A262983C4",
paperPrintScale:100,
scrollbars:33,
size:"1001,585",
styleName:null,
typeid:3,
uuid:"1E1ED529-45BC-4FF5-AA37-A39CA5DEFC95",
view:0