customProperties:"useCssPosition:true",
dataSource:"db:/avanti/sch_milestone_group",
extendsID:"2B7C2B89-6740-4771-8CA6-BD1B59E74A40",
items:[
{
partType:2,
typeid:19,
uuid:"0D515F38-2894-4CAB-BC6C-D3BCB91F3D37"
},
{
height:136,
partType:8,
typeid:19,
uuid:"93626013-12BD-48AE-98EC-F6BA38BD57C0"
},
{
height:99,
partType:5,
typeid:19,
uuid:"BC2E9ED5-8737-44EC-82A2-014C8D8C3885"
},
{
cssPosition:"10,-1,-1,4,717,82",
json:{
columns:[
{
dataprovider:"sch_milestone_group_to_sys_cost_centre.cc_desc",
id:"sch_milestone_group_to_sys_cost_centre.cc_desc",
styleClassDataprovider:null,
svyUUID:"2F1D978D-1521-45E0-AB09-9C9690AE7D04"
},
{
dataprovider:"sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1_placehold",
id:"sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_1_placehold",
styleClassDataprovider:null,
svyUUID:"16828E20-6A61-4521-836D-2A31601132A2"
},
{
dataprovider:"qa_check_pos_1",
id:"qa_check_pos_1",
styleClassDataprovider:null,
svyUUID:"93E169A3-8149-40C0-97D6-22FA1E0A3295"
},
{
dataprovider:"sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2_placehold",
id:"sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_2_placehold",
styleClassDataprovider:null,
svyUUID:"5A2C43D5-6E8A-417B-8E8D-B8AD164195B1"
},
{
dataprovider:"qa_check_pos_2",
id:"qa_check_pos_2",
styleClassDataprovider:null,
svyUUID:"1D5F4028-911D-4FAB-BD72-66DB685564DF"
},
{
dataprovider:"sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3_placehold",
id:"sch_milestone_group_to_sys_cost_centre.cc_qa_check_field_3_placehold",
styleClassDataprovider:null,
svyUUID:"030649CF-4B4D-4AF1-901A-6302B86F6282"
},
{
dataprovider:"qa_check_pos_3",
id:"qa_check_pos_3",
styleClassDataprovider:null,
svyUUID:"E522A020-CBD1-4124-9959-A71C8A811DBC"
}
],
cssPosition:{
bottom:"-1",
height:"82",
left:"4",
right:"-1",
top:"10",
width:"717"
}
},
name:"grid",
typeName:"aggrid-groupingtable",
typeid:47,
uuid:"D351E8B0-BE3C-4366-93ED-180EE3134977"
},
{
cssPosition:"103,-1,-1,613,80,30",
json:{
cssPosition:{
bottom:"-1",
height:"30",
left:"613",
right:"-1",
top:"103",
width:"80"
},
onActionMethodID:"5A5ED3B0-8A3C-4F73-B02C-9553B3A86DD9",
text:"Cancel"
},
name:"button_9c",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"D44AB23B-F9B2-4435-A60A-FFE35FA50D56"
},
{
cssPosition:"103,-1,-1,521,80,30",
json:{
cssPosition:{
bottom:"-1",
height:"30",
left:"521",
right:"-1",
top:"103",
width:"80"
},
onActionMethodID:"37EEF227-32AB-4C88-9028-A8091B75EE23",
text:"OK"
},
name:"button_9",
typeName:"bootstrapcomponents-button",
typeid:47,
uuid:"EB6C4624-3C3F-4708-BECD-0D5A3AEBE14C"
}
],
name:"sf_main_dtl_labour_qa_checkss",
onRenderMethodID:"-1",
onShowMethodID:"-1",
scrollbars:33,
size:"757,136",
styleName:"Avanti",
typeid:3,
uuid:"62DFE3E3-23CE-4C50-AD21-8C6BA0D9FE23",
view:1